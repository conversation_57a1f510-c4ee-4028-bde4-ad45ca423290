{"grammars": [{"name": "rust", "camelcase": "Rust", "scope": "source.rust", "path": ".", "file-types": ["rs"], "highlights": ["queries/highlights.scm"], "injections": ["queries/injections.scm"], "tags": ["queries/tags.scm"], "injection-regex": "rust"}], "metadata": {"version": "0.23.3", "license": "MIT", "description": "Rust grammar for tree-sitter", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "maxbrun<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "links": {"repository": "https://github.com/tree-sitter/tree-sitter-rust"}}, "bindings": {"c": true, "go": true, "node": true, "python": true, "rust": true, "swift": true}}