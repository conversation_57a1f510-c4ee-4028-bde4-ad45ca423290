import { spawn, ChildProcess } from 'child_process';
import { Tool, Too<PERSON><PERSON>arameter, ToolResult } from '../tool-manager';
import { Logger } from '../../../utils/logger';
import { GRPCClient } from '../../../services/grpc-client';

/**
 * LaunchProcessTool - Terminal command execution
 * 
 * Replicates Claude Code's launch-process functionality for executing
 * shell commands with support for both waiting and non-waiting processes.
 */
export class LaunchProcessTool implements Tool {
    name = 'launch-process';
    description = 'Launch a new process with a shell command. Supports both waiting and non-waiting execution modes.';
    
    parameters: ToolParameter[] = [
        {
            name: 'command',
            type: 'string',
            description: 'The shell command to execute',
            required: true
        },
        {
            name: 'wait',
            type: 'boolean',
            description: 'Whether to wait for the command to complete',
            required: true
        },
        {
            name: 'max_wait_seconds',
            type: 'number',
            description: 'Number of seconds to wait for the command to complete. Only relevant when wait=true',
            required: true
        },
        {
            name: 'cwd',
            type: 'string',
            description: 'Working directory for the command. If not supplied, uses the current working directory',
            required: false
        }
    ];

    private activeProcesses: Map<number, ChildProcess> = new Map();
    private processCounter: number = 1;

    constructor(
        private logger: Logger,
        private grpcClient: GRPCClient
    ) {
        this.logger = logger.withPrefix('LaunchProcess');
    }

    async execute(parameters: Record<string, any>): Promise<ToolResult> {
        try {
            const { command, wait, max_wait_seconds, cwd } = parameters;

            this.logger.info('Launching process', { 
                command: command.substring(0, 100),
                wait,
                maxWaitSeconds: max_wait_seconds,
                cwd: cwd || process.cwd()
            });

            if (wait) {
                return await this.executeWaitingProcess(command, max_wait_seconds, cwd);
            } else {
                return await this.executeBackgroundProcess(command, cwd);
            }

        } catch (error) {
            this.logger.error('Process launch failed:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    private async executeWaitingProcess(
        command: string, 
        maxWaitSeconds: number, 
        cwd?: string
    ): Promise<ToolResult> {
        return new Promise((resolve) => {
            const startTime = Date.now();
            let stdout = '';
            let stderr = '';
            let timedOut = false;

            // Parse command and arguments
            const { cmd, args } = this.parseCommand(command);

            // Spawn process
            const childProcess = spawn(cmd, args, {
                cwd: cwd || process.cwd(),
                shell: true,
                stdio: ['pipe', 'pipe', 'pipe']
            });

            // Set timeout
            const timeout = setTimeout(() => {
                timedOut = true;
                childProcess.kill('SIGTERM');
                
                setTimeout(() => {
                    if (!childProcess.killed) {
                        childProcess.kill('SIGKILL');
                    }
                }, 5000); // Force kill after 5 seconds
            }, maxWaitSeconds * 1000);

            // Collect output
            if (childProcess.stdout) {
                childProcess.stdout.on('data', (data) => {
                    stdout += data.toString();
                });
            }

            if (childProcess.stderr) {
                childProcess.stderr.on('data', (data) => {
                    stderr += data.toString();
                });
            }

            // Handle process completion
            childProcess.on('close', (code, signal) => {
                clearTimeout(timeout);
                const executionTime = Date.now() - startTime;

                if (timedOut) {
                    this.logger.warn('Process timed out', { 
                        command: command.substring(0, 100),
                        executionTime,
                        maxWaitSeconds
                    });

                    resolve({
                        success: false,
                        error: `Process timed out after ${maxWaitSeconds} seconds`,
                        result: {
                            stdout: stdout.trim(),
                            stderr: stderr.trim(),
                            exitCode: null,
                            signal,
                            timedOut: true,
                            executionTime
                        }
                    });
                } else {
                    const success = code === 0;
                    
                    this.logger.info('Process completed', {
                        command: command.substring(0, 100),
                        exitCode: code,
                        signal,
                        executionTime,
                        success
                    });

                    resolve({
                        success,
                        result: {
                            stdout: stdout.trim(),
                            stderr: stderr.trim(),
                            exitCode: code,
                            signal,
                            timedOut: false,
                            executionTime
                        },
                        error: success ? undefined : `Process exited with code ${code}`
                    });
                }
            });

            // Handle process errors
            childProcess.on('error', (error) => {
                clearTimeout(timeout);
                const executionTime = Date.now() - startTime;

                this.logger.error('Process error', {
                    command: command.substring(0, 100),
                    error: error.message,
                    executionTime
                });

                resolve({
                    success: false,
                    error: `Process error: ${error.message}`,
                    result: {
                        stdout: stdout.trim(),
                        stderr: stderr.trim(),
                        exitCode: null,
                        signal: null,
                        timedOut: false,
                        executionTime
                    }
                });
            });
        });
    }

    private async executeBackgroundProcess(command: string, cwd?: string): Promise<ToolResult> {
        try {
            const terminalId = this.processCounter++;
            
            // Parse command and arguments
            const { cmd, args } = this.parseCommand(command);

            // Spawn background process
            const childProcess = spawn(cmd, args, {
                cwd: cwd || process.cwd(),
                shell: true,
                stdio: ['pipe', 'pipe', 'pipe'],
                detached: false
            });

            // Store process reference
            this.activeProcesses.set(terminalId, childProcess);

            // Set up process event handlers
            childProcess.on('close', (code, signal) => {
                this.logger.info('Background process completed', {
                    terminalId,
                    command: command.substring(0, 100),
                    exitCode: code,
                    signal
                });
                this.activeProcesses.delete(terminalId);
            });

            childProcess.on('error', (error) => {
                this.logger.error('Background process error', {
                    terminalId,
                    command: command.substring(0, 100),
                    error: error.message
                });
                this.activeProcesses.delete(terminalId);
            });

            this.logger.info('Background process launched', {
                terminalId,
                command: command.substring(0, 100),
                pid: childProcess.pid
            });

            return {
                success: true,
                result: {
                    terminalId,
                    pid: childProcess.pid,
                    command,
                    cwd: cwd || process.cwd(),
                    startTime: new Date().toISOString()
                }
            };

        } catch (error) {
            this.logger.error('Failed to launch background process:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    private parseCommand(command: string): { cmd: string; args: string[] } {
        // Simple command parsing - in production, you'd want more sophisticated parsing
        const parts = command.trim().split(/\s+/);
        const cmd = parts[0];
        const args = parts.slice(1);
        
        return { cmd, args };
    }

    // Helper method to get active processes (used by other tools)
    getActiveProcesses(): Map<number, ChildProcess> {
        return this.activeProcesses;
    }

    // Helper method to get process by terminal ID
    getProcess(terminalId: number): ChildProcess | undefined {
        return this.activeProcesses.get(terminalId);
    }

    // Helper method to kill a process
    killProcess(terminalId: number): boolean {
        const process = this.activeProcesses.get(terminalId);
        if (process) {
            process.kill('SIGTERM');
            this.activeProcesses.delete(terminalId);
            return true;
        }
        return false;
    }
}
