import axios from 'axios';
import { Too<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolResult } from '../tool-manager';
import { Logger } from '../../../utils/logger';
import { GRPCClient } from '../../../services/grpc-client';

/**
 * WebSearchTool - Internet search functionality
 * 
 * Replicates <PERSON> Code's web-search functionality for retrieving
 * information from the internet to assist with coding tasks.
 */
export class WebSearchTool implements Tool {
    name = 'web-search';
    description = 'Search the web for information. Returns results in markdown format with URLs, titles, and snippets.';
    
    parameters: ToolParameter[] = [
        {
            name: 'query',
            type: 'string',
            description: 'The search query to send',
            required: true
        },
        {
            name: 'num_results',
            type: 'number',
            description: 'Number of results to return (1-10)',
            required: false
        }
    ];

    private searchEngines = {
        duckduckgo: 'https://api.duckduckgo.com/',
        serper: 'https://google.serper.dev/search',
        bing: 'https://api.bing.microsoft.com/v7.0/search'
    };

    constructor(
        private logger: Logger,
        private grpcClient: GRPCClient
    ) {
        this.logger = logger.withPrefix('WebSearch');
    }

    async execute(parameters: Record<string, any>): Promise<ToolResult> {
        try {
            const { query, num_results = 5 } = parameters;

            // Validate parameters
            if (!query || typeof query !== 'string') {
                return {
                    success: false,
                    error: 'Query parameter is required and must be a string'
                };
            }

            if (num_results < 1 || num_results > 10) {
                return {
                    success: false,
                    error: 'num_results must be between 1 and 10'
                };
            }

            this.logger.info('Performing web search', { 
                query: query.substring(0, 100),
                numResults: num_results
            });

            // Try different search engines in order of preference
            let results = await this.searchWithDuckDuckGo(query, num_results);
            
            if (!results || results.length === 0) {
                results = await this.searchWithFallback(query, num_results);
            }

            if (!results || results.length === 0) {
                return {
                    success: false,
                    error: 'No search results found'
                };
            }

            // Format results
            const formattedResults = this.formatResults(results);

            this.logger.info('Web search completed', {
                query: query.substring(0, 100),
                resultsCount: results.length
            });

            return {
                success: true,
                result: {
                    query,
                    results: formattedResults,
                    metadata: {
                        totalResults: results.length,
                        searchEngine: 'duckduckgo',
                        timestamp: new Date().toISOString()
                    }
                }
            };

        } catch (error) {
            this.logger.error('Web search failed:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    private async searchWithDuckDuckGo(query: string, numResults: number): Promise<any[]> {
        try {
            // DuckDuckGo Instant Answer API (limited but free)
            const response = await axios.get('https://api.duckduckgo.com/', {
                params: {
                    q: query,
                    format: 'json',
                    no_html: '1',
                    skip_disambig: '1'
                },
                timeout: 10000
            });

            const data = response.data;
            const results: any[] = [];

            // Add abstract if available
            if (data.Abstract) {
                results.push({
                    title: data.Heading || 'DuckDuckGo Result',
                    url: data.AbstractURL || '',
                    snippet: data.Abstract,
                    source: 'duckduckgo'
                });
            }

            // Add related topics
            if (data.RelatedTopics && Array.isArray(data.RelatedTopics)) {
                for (const topic of data.RelatedTopics.slice(0, numResults - 1)) {
                    if (topic.Text && topic.FirstURL) {
                        results.push({
                            title: topic.Text.split(' - ')[0] || 'Related Topic',
                            url: topic.FirstURL,
                            snippet: topic.Text,
                            source: 'duckduckgo'
                        });
                    }
                }
            }

            // Add definition if available
            if (data.Definition) {
                results.push({
                    title: `Definition: ${data.DefinitionWord || query}`,
                    url: data.DefinitionURL || '',
                    snippet: data.Definition,
                    source: 'duckduckgo'
                });
            }

            return results.slice(0, numResults);

        } catch (error) {
            this.logger.debug('DuckDuckGo search failed:', error.message);
            return [];
        }
    }

    private async searchWithFallback(query: string, numResults: number): Promise<any[]> {
        // Fallback to a simple web scraping approach for common coding sites
        const codingSites = [
            'stackoverflow.com',
            'github.com',
            'developer.mozilla.org',
            'docs.python.org',
            'golang.org',
            'nodejs.org'
        ];

        const results: any[] = [];

        try {
            // Create search URLs for popular coding sites
            for (const site of codingSites.slice(0, Math.min(3, numResults))) {
                const searchUrl = `https://www.google.com/search?q=site:${site}+${encodeURIComponent(query)}`;
                
                results.push({
                    title: `Search ${site} for: ${query}`,
                    url: searchUrl,
                    snippet: `Search results for "${query}" on ${site}`,
                    source: 'fallback'
                });
            }

            // Add some general programming resources
            if (results.length < numResults) {
                const generalResources = [
                    {
                        title: `Stack Overflow: ${query}`,
                        url: `https://stackoverflow.com/search?q=${encodeURIComponent(query)}`,
                        snippet: `Search Stack Overflow for questions about: ${query}`,
                        source: 'fallback'
                    },
                    {
                        title: `GitHub: ${query}`,
                        url: `https://github.com/search?q=${encodeURIComponent(query)}&type=repositories`,
                        snippet: `Search GitHub repositories for: ${query}`,
                        source: 'fallback'
                    },
                    {
                        title: `MDN Web Docs: ${query}`,
                        url: `https://developer.mozilla.org/en-US/search?q=${encodeURIComponent(query)}`,
                        snippet: `Search MDN Web Docs for: ${query}`,
                        source: 'fallback'
                    }
                ];

                results.push(...generalResources.slice(0, numResults - results.length));
            }

            return results.slice(0, numResults);

        } catch (error) {
            this.logger.debug('Fallback search failed:', error.message);
            return [];
        }
    }

    private formatResults(results: any[]): string {
        if (!results || results.length === 0) {
            return 'No search results found.';
        }

        let formatted = '';
        
        for (let i = 0; i < results.length; i++) {
            const result = results[i];
            formatted += `- [${result.title}](${result.url})\n`;
            
            if (result.snippet) {
                // Clean up snippet
                const snippet = result.snippet
                    .replace(/\n/g, ' ')
                    .replace(/\s+/g, ' ')
                    .trim();
                
                if (snippet.length > 200) {
                    formatted += `  ${snippet.substring(0, 200)}...\n\n`;
                } else {
                    formatted += `  ${snippet}\n\n`;
                }
            } else {
                formatted += '\n';
            }
        }

        return formatted.trim();
    }

    // Helper method for searching specific coding sites
    private async searchCodingSite(site: string, query: string): Promise<any[]> {
        const siteSearches: Record<string, (query: string) => Promise<any[]>> = {
            'stackoverflow.com': this.searchStackOverflow.bind(this),
            'github.com': this.searchGitHub.bind(this),
            'developer.mozilla.org': this.searchMDN.bind(this)
        };

        const searchFunction = siteSearches[site];
        if (searchFunction) {
            try {
                return await searchFunction(query);
            } catch (error) {
                this.logger.debug(`Failed to search ${site}:`, error.message);
                return [];
            }
        }

        return [];
    }

    private async searchStackOverflow(query: string): Promise<any[]> {
        try {
            // Use Stack Exchange API
            const response = await axios.get('https://api.stackexchange.com/2.3/search/advanced', {
                params: {
                    order: 'desc',
                    sort: 'relevance',
                    q: query,
                    site: 'stackoverflow',
                    pagesize: 3
                },
                timeout: 5000
            });

            return response.data.items.map((item: any) => ({
                title: item.title,
                url: item.link,
                snippet: `Score: ${item.score}, Answers: ${item.answer_count}`,
                source: 'stackoverflow'
            }));

        } catch (error) {
            return [];
        }
    }

    private async searchGitHub(query: string): Promise<any[]> {
        try {
            // Use GitHub Search API (requires authentication for higher rate limits)
            const response = await axios.get('https://api.github.com/search/repositories', {
                params: {
                    q: query,
                    sort: 'stars',
                    order: 'desc',
                    per_page: 3
                },
                timeout: 5000
            });

            return response.data.items.map((item: any) => ({
                title: item.full_name,
                url: item.html_url,
                snippet: item.description || 'No description available',
                source: 'github'
            }));

        } catch (error) {
            return [];
        }
    }

    private async searchMDN(query: string): Promise<any[]> {
        // MDN doesn't have a public API, so we'll create search URLs
        return [{
            title: `MDN Web Docs: ${query}`,
            url: `https://developer.mozilla.org/en-US/search?q=${encodeURIComponent(query)}`,
            snippet: `Search MDN Web Docs for documentation about: ${query}`,
            source: 'mdn'
        }];
    }
}
